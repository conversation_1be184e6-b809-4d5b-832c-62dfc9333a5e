// For more information about this file see https://dove.feathersjs.com/guides/cli/service.shared.html
import type { Params } from '@feathersjs/feathers'
import type { ClientApplication } from '../../client.js'
import type {
  Challenges,
  ChallengesData,
  ChallengesPatch,
  ChallengesQuery,
  ChallengesService
} from './challenges.class.js'

export type { Challenges, ChallengesData, ChallengesPatch, ChallengesQuery }

export type ChallengesClientService = Pick<
  ChallengesService<Params<ChallengesQuery>>,
  (typeof challengesMethods)[number]
>

export const challengesPath = 'challenges'

export const challengesMethods = ['find', 'get', 'create', 'patch', 'remove'] as const

export const challengesClient = (client: ClientApplication) => {
  const connection = client.get('connection')

  client.use(challengesPath, connection.service(challengesPath), {
    methods: challengesMethods
  })
}

// Add this service to the client service type index
declare module '../../client.js' {
  interface ServiceTypes {
    [challengesPath]: ChallengesClientService
  }
}
