// For more information about this file see https://dove.feathersjs.com/guides/cli/service.schemas.html
import {resolve, getValidator, querySyntax} from '@feathersjs/schema'
import {ObjectIdSchema} from '@feathersjs/schema'
import type {FromSchema} from '@feathersjs/schema'
import {passwordHash} from '@feathersjs/authentication-local';

import type {HookContext} from '../../declarations.js'
import {dataValidator, queryValidator} from '../../validators.js'
import {
    addToSet,
    commonFields,
    commonPatch,
    commonQueries,
    emailHandler,
    nullable
} from "../../utils/common/schemas.js";
import {AnyObj} from 'feathers-ucan';

// Main data model schema
export const loginsSchema = {
    $id: 'Logins',
    type: 'object',
    additionalProperties: true,
    required: ['_id'],
    properties: {
        _id: ObjectIdSchema(),
        name: {type: 'string'},
        email: {type: 'string'},
        phone: {type: 'string'},
        password: {type: 'string'},
        pendingPassword: nullable.string,
        did: {type: 'string'},
        ucan: {type: 'string'},
        fingerprints: {type: 'array', items: ObjectIdSchema()},
        owner: ObjectIdSchema(), //ppls
        keyPair: {
            type: 'object',
            properties: {
                publicKey: {type: 'string'},
                privateKey: {type: 'string'},
                alg: {type: 'string'}
            }
        },
        googleId: {type: 'string'},
        facebookId: {type: 'string'},
        twitterId: {type: 'string'},
        linkedinId: {type: 'string'},
        microsoftId: {type: 'string'},
        githubId: {type: 'string'},
        appleId: {type: 'string'},
        isVerified: {type: 'boolean'},
        verifyToken: nullable.string,
        verifyExpires: {},
        lastLogin: {},
        loginAttempts: { type: 'array', items: {}},
        locked: {},
        lastLoginMethod: {type: 'string'},
        resetToken: nullable.string,
        resetExpires: {anyOf: [{type: 'string'}, {type: 'object'}, {type: 'null'}]},
        ...commonFields.properties
    }
} as const
export type Logins = FromSchema<typeof loginsSchema>
export const loginsValidator = getValidator(loginsSchema, dataValidator)
export const loginsResolver = resolve<Logins, HookContext>({})

export const loginsExternalResolver = resolve<Logins, HookContext>({})

const allDataResolvers = () => {
    return ({
        password: passwordHash({strategy: 'local'}) as any,
        pendingPassword: async (val:any, data, context):Promise<null|string|undefined> => {
            if (val) return passwordHash({strategy: 'local'})(val, data, context);
            return val
        },
        email: emailHandler({ throw: true })
    })
}

// Schema for creating new data
export const loginsDataSchema = {
    $id: 'LoginsData',
    type: 'object',
    additionalProperties: false,
    required: [],
    properties: {
        ...loginsSchema.properties
    }
} as const
export type LoginsData = FromSchema<typeof loginsDataSchema>
export const loginsDataValidator = getValidator(loginsDataSchema, dataValidator)
export const loginsDataResolver = resolve<LoginsData, HookContext>({
    properties: {
       ...allDataResolvers()
    }
})

const pushPull = [
    {path: 'loginAttempts', type: {}}
]
// Schema for updating existing data
export const loginsPatchSchema = {
    $id: 'LoginsPatch',
    type: 'object',
    additionalProperties: true,
    required: [],
    properties: {
        ...loginsSchema.properties,
        ...commonPatch(loginsSchema.properties).properties,
        $push: addToSet(pushPull)
    }
} as const
export type LoginsPatch = FromSchema<typeof loginsPatchSchema>
export const loginsPatchValidator = getValidator(loginsPatchSchema, dataValidator)
export const loginsPatchResolver = resolve<LoginsPatch, HookContext>({
    properties: {
        ...allDataResolvers()
    }
})

// Schema for allowed query properties
export const loginsQuerySchema = {
    $id: 'LoginsQuery',
    type: 'object',
    additionalProperties: true,
    properties: {
        ...querySyntax({
            ...loginsSchema.properties,
            ...commonQueries.properties,
            name: {},
            email: {},
            phone: {}
        }),
        _limit_to: {}
    }
} as const
export type LoginsQuery = FromSchema<typeof loginsQuerySchema>
export const loginsQueryValidator = getValidator(loginsQuerySchema, queryValidator)
export const loginsQueryResolver = resolve<LoginsQuery, HookContext>({})
