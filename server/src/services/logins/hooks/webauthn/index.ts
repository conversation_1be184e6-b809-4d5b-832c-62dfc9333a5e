import {
    generateRegistrationOptions,
    generateAuthenticationOptions,
} from '@simplewebauthn/server';

import {BadRequest, NotAuthenticated} from '@feathersjs/errors';
import {HookContext} from '../../../../declarations.js';
interface Data {
    action: 'registrationOptions' | 'registrationVerify' | 'authenticationOptions' | 'authenticationVerify';
    loginId?: string;
    username?: string;
    displayName?: string;
    credential?: any;
}

import crypto from 'node:crypto'
import {CoreCall} from 'feathers-ucan';
import { assertOriginAndRpId} from '../../../../authentication/strategies/webauthn/index.js';

const toUserHandle = (userId: string): Uint8Array => {
    const enc = new TextEncoder();
    const bytes = enc.encode(userId);
    if (bytes.length <= 64) return bytes;
    // If longer than 64 bytes, hash to 32 bytes
    return new Uint8Array(crypto.createHash('sha256').update(bytes).digest());
}

export const webAuthnOptions = (action: 'register' | 'authenticate') => {
    return async (context: HookContext) => {

        const loginOptions = context.params.query?.loginOptions || {};
        const {usernameField = 'email', rpID:clientRpId} = loginOptions;
        switch (action) {
            case 'register': {
                if (!context.result._id) throw new BadRequest('loginId is required');

                // const existing = await new CoreCall('passkeys', context).find({
                //     query: { login: context.result._id, rpID: context.params.loginOptions.rpID },
                //     paginate: false
                // }) as any[];
// v13 wants id: Uint8Array
//                     const excludeCredentials = existing.map(pk => ({
//                         id: b64urlToBytes(pk.credentialId),      // Uint8Array
//                         // type: 'public-key' as const,
//                         transports: pk.transports || undefined,  // AuthenticatorTransportFuture[] | undefined
//                     }));

                const { rpID } = assertOriginAndRpId(clientRpId)(context);
                const userName = context.result[usernameField]
                const options = await generateRegistrationOptions({
                    rpName: 'Example App', // Shown in browser
                    rpID: rpID,
                    userID: toUserHandle(context.result._id),
                    userName,
                    userDisplayName: context.result.name || context.result._fastjoin?.owner?.name || userName,
                    attestationType: 'none',
                    authenticatorSelection: {
                        residentKey: 'preferred',
                        userVerification: 'preferred',
                    },
                    // Exclude previously registered passkeys
                    // excludeCredentials: excludeCredentials.length ? excludeCredentials : undefined,
                });

                await new CoreCall('challenges', context).create({
                    challenge: options.challenge,
                    login: context.result._id,
                    connectionId: context.params.connection.id,
                    expiresAt: new Date(Date.now() + 5 * 60 * 1000),
                    kind: 'registration'
                });
                context.result._fastjoin = { ...context.result._fastjoin, webauthnOptions: options }
                // Store challenge server-side for verification

                return context;
            }

            case 'authenticate': {
                // Retrieve ALL passkeys for this user (not filtered by credential.id)
                // The credential.id will be provided later during verification
                const passkeys = await new CoreCall('passkeys', context).find({
                    query: {login: context.result._id},
                    paginate: false
                });

                // Fix logic error: should check if NO passkeys found
                if (!passkeys.data?.length) throw new NotAuthenticated('No passkeys found for user');

                const allowCredentials = passkeys.data.map((pk: any) => ({
                    id: pk.credentialId,
                    transports: pk.transports || [],
                }));

                const { rpID } = assertOriginAndRpId(clientRpId)(context);
                const options = await generateAuthenticationOptions({
                    rpID,
                    allowCredentials,
                    userVerification: 'preferred',
                });

                await new CoreCall('challenges', context).create({
                    challenge: options.challenge,
                    login: context.result._id,
                    connectionId: context.params.connection.id,
                    expiresAt: new Date(Date.now() + 5 * 60 * 1000),
                    kind: 'authentication'
                });
                context.result._fastjoin = { ...context.result._fastjoin, webauthnOptions: options }

                return context;
            }

            default:
                throw new BadRequest(`Unknown action: ${action}`);
        }
    }
}
