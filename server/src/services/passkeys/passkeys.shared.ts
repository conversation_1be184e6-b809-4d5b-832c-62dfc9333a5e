// For more information about this file see https://dove.feathersjs.com/guides/cli/service.shared.html
import type { Params } from '@feathersjs/feathers'
import type { ClientApplication } from '../../client.js'
import type { Passkeys, PasskeysData, PasskeysPatch, PasskeysQuery, PasskeysService } from './passkeys.class.js'

export type { Passkeys, PasskeysData, PasskeysPatch, PasskeysQuery }

export type PasskeysClientService = Pick<
  PasskeysService<Params<PasskeysQuery>>,
  (typeof passkeysMethods)[number]
>

export const passkeysPath = 'passkeys'

export const passkeysMethods = ['find', 'get', 'create', 'patch', 'remove'] as const

export const passkeysClient = (client: ClientApplication) => {
  const connection = client.get('connection')

  client.use(passkeysPath, connection.service(passkeysPath), {
    methods: passkeysMethods
  })
}

// Add this service to the client service type index
declare module '../../client.js' {
  interface ServiceTypes {
    [passkeysPath]: PasskeysClientService
  }
}
