// src/auth/strategies/webauthn.ts
import { LocalStrategy } from '@feathersjs/authentication-local';
import {
    verifyAuthenticationResponse,
    type WebAuthnCredential,
    type AuthenticatorTransportFuture, verifyRegistrationResponse
} from '@simplewebauthn/server';
import {NotAuthenticated, BadRequest} from '@feathersjs/errors';
import {_unset} from '../../../utils/index.js';
import {parse} from 'psl';

const deriveRpId = (origin: string) => {
    const host = new URL(origin).hostname;
    if (host === 'localhost' || /^\d{1,3}(\.\d{1,3}){3}$/.test(host)) return host;
    const p = parse(host);
    if ('error' in p || !p.domain) throw new Error('Invalid origin');
    return p.domain;
};

export const assertOriginAndRpId = (rpID?:string) => {
    return ({app, params}:any) => {
        const expectedOrigins: string[] = (app.get('authentication') as any).webauthn.expectedOrigin || [];
        const handshakeOrigin = params.connection?.origin
            || params.headers?.origin
            || params.origin; // fallback if you set it

        if (!handshakeOrigin || !expectedOrigins.includes(handshakeOrigin)) {
            throw new NotAuthenticated('Origin not allowed');
        }

        const derived = deriveRpId(handshakeOrigin);
        if (rpID && rpID !== derived) {
            throw new NotAuthenticated('rpId mismatch');
        }
        return {handshakeOrigin, rpID: derived};
    }
}


export const registerWebAuthnDevice = async ({ credential, login, expectedOrigin, allowedRpIds, passkeyCreateFn, rpID, expectedChallenge}:any) => {
    const toB64Url = (u8: Uint8Array) => Buffer.from(u8).toString('base64url');

    const verification = await verifyRegistrationResponse({
        response: credential,
        expectedChallenge,
        expectedOrigin,  // string | string[]
        expectedRPID: allowedRpIds,               // string (the rpID you derived)
    });

    if (!verification.verified) throw new Error('WebAuthn verification failed');
    const info = verification.registrationInfo!;
    const cred = info.credential;

// cred.id is Base64URLString (string), cred.publicKey is Uint8Array
    const credentialIdB64 = cred.id;                   // already base64url
    const publicKeyB64    = toB64Url(cred.publicKey);  // convert bytes -> base64url
    const signCount       = cred.counter;
    const transports      = cred.transports;           // optional string[]

    const aaguid          = info.aaguid;
    const deviceType      = info.credentialDeviceType; // 'singleDevice' | 'multiDevice'
    const backedUp        = info.credentialBackedUp;   // boolean
    // const rpIDFromLib     = info.rpID ?? rpID;         // optional in v13

// Optional mapping to your schema’s backup fields
    const backupEligible  = deviceType === 'multiDevice';
    const backupState     = backedUp ? 'enabled' : 'disabled';

    await passkeyCreateFn({
        login: login._id,
        kind: 'registration',
        rpID: rpID,                 // store per-credential
        credentialId: credentialIdB64,     // base64url string
        publicKey: publicKeyB64,           // base64url string
        signCount,                         // number
        transports,                        // string[] | undefined
        aaguid,                            // string
        backupEligible,                    // boolean
        backupState,                       // 'enabled' | 'disabled'
        // displayName: optional user-provided label later
    });
    return verification;
}

export class WebAuthnStrategy extends LocalStrategy {

    async findEntity(username, params) {
        const {usernameField} = params.loginOptions || {};
        if (!username) { // don't query for users without any condition set.
            throw new NotAuthenticated(`No ${usernameField} passed to webauthn`);
        }

        const query = await this.getEntityQuery({
            [usernameField]: username
        }, params);

        const findParams = Object.assign({}, params, {query});
        const entityService = this.entityService;

        const result = await entityService.find({...findParams, skipJoins: true});
        const list = Array.isArray(result) ? result : result.data;

        if (!Array.isArray(list) || list.length === 0) {
            throw new NotAuthenticated(`No login found for ${usernameField} ${username}`);
        }

        const [entity] = list;

        return entity;
    }

    async authenticate(data: any, params: any) {
        const app = this.app as any;
        const { credential, register, email, rpID:clientRpId } = data || {};

        const { rpID } = assertOriginAndRpId(clientRpId)(params);
        const { usernameField } = params.loginOptions || email ? { usernameField: 'email' } : { usernameField: 'phone' };
        params.loginOptions ? params.loginOptions.usernameField = usernameField : params.loginOptions = { usernameField };

        const username = data[usernameField];
        const login = await this.findEntity(username, _unset(params, 'provider'));

        if (!credential) throw new BadRequest('Missing credential');
        const { allowedRpIds, expectedOrigin } = app.get('authentication').webauthn;
        const passkeys = app.service('passkeys');
        const challenges = app.service('challenges');

        let verification:any = {}
        if(register){
            const last = await challenges.find({
                query: {login: login._id, kind: 'registration', $limit: 1, $sort: {createdAt: -1}},
            });
            const expectedChallenge = last?.data?.[0]?.challenge;
            if(!expectedChallenge) throw new NotAuthenticated('No challenge found');
            if(new Date().getTime() > new Date(last?.data?.[0]?.expiresAt).getTime()) throw new NotAuthenticated('Expired challenge');
           verification = await registerWebAuthnDevice({
               credential,
               login,
               expectedOrigin,
               allowedRpIds,
               passkeyCreateFn: async (d) => await passkeys.create(d),
               rpID
           });
            if (!verification.verified) throw new NotAuthenticated('WebAuthn verification failed');

        } else {
            // Find authenticator by credentialId
            const credIdB64 = credential.id;
            const [authenticator] = await passkeys.find({
                query: {credentialId: credIdB64},
                paginate: false
            }) as any[];
            if (!authenticator) throw new NotAuthenticated('Unknown credential');

            // Get latest auth challenge (you can store by challenge value too)
            const last = await challenges.find({
                query: {login: login._id, kind: 'authentication', $limit: 1, $sort: {createdAt: -1}},
            });
            const expectedChallenge = last?.data?.[0]?.challenge;
            if (!expectedChallenge) throw new NotAuthenticated('Expired challenge');
            if(new Date().getTime() > new Date(last?.data?.[0]?.expiresAt).getTime()) throw new NotAuthenticated('Expired challenge');
            const b64urlToUint8 = (s: string) => Uint8Array.from(Buffer.from(s, 'base64url'));


            const credentialRecord: WebAuthnCredential = {
                id: authenticator.credentialId,                            // base64url string
                publicKey: b64urlToUint8(authenticator.publicKey),         // Uint8Array
                counter: authenticator.signCount ?? 0,
                transports: authenticator.transports as AuthenticatorTransportFuture[] | undefined,
            };

            verification = await verifyAuthenticationResponse({
                response: credential,          // <- what startAuthentication(...) returned on the client
                expectedChallenge,                  // string or (challenge)=>boolean
                expectedOrigin: expectedOrigin.split(' '),    // string | string[]
                expectedRPID: allowedRpIds.split(' '),                 // string | string[]
                credential: credentialRecord,       // <- v13 expects `credential`, not `authenticator`
                requireUserVerification: true,
            });
            if (!verification.verified) throw new NotAuthenticated('WebAuthn verification failed');

            const { newCounter } = verification.authenticationInfo!;

            await passkeys.patch(authenticator._id, { signCount: newCounter });
        }

        const { entity } = this.configuration;

        await this.entityService.patch(login._id, { lastLogin: new Date(), lastLoginMethod: 'webauthn', isVerified: true, $unset: { verifyToken: '', verifyExpires: '', resetToken: '', resetExpires: '' } }, { admin_pass: true } as any);

        return {
            authentication: { strategy: this.name as string },
            [entity]: login
        };
    }
}
