import {Application, HookContext} from "../declarations.js";
import {NotAuthenticated} from "@feathersjs/errors";
import {oauth as expressOauth} from '@feathersjs/authentication-oauth';
import {_get, _set, _unset} from "symbol-ucan";
import {
    AuthService,
    UcanStrategy
} from 'feathers-ucan';
import {AuthenticationService} from '@feathersjs/authentication';
import {LocalAuth} from './strategies/local-strategy.js'
import { WebAuthnStrategy } from './strategies/webauthn/index.js';
// import {AuthenticationParams, AuthenticationRequest} from "@feathersjs/authentication/src/core";
import {GoogleStrategy, LinkedinStrategy} from './strategies/oauth/index.js';
import {ApiKeyStrategy} from './strategies/api-key/index.js';

const _pick = <T>(obj: T, keyList: Array<string>) => {
    let newObj: Partial<T> = {};
    keyList.forEach(a => newObj = _set(newObj, a, _get<T, any>(obj, a)));
};

declare module '../declarations.js' {
    interface ServiceTypes {
        authentication: AuthenticationService
    }
}

export const oAuthStrategies = ['google', 'linkedin']

export default (app: Application) => {
    const authentication = new AuthService(app);

    authentication.register('jwt', new UcanStrategy());
    authentication.register('local', new LocalAuth());
    authentication.register('webauthn', new WebAuthnStrategy());
    authentication.register('google', new GoogleStrategy(app));
    authentication.register('linkedin', new LinkedinStrategy(app));
    authentication.register('apiKey', new ApiKeyStrategy(app));
    const configKey = 'authentication';

    app.use('authentication', authentication);
    app.configure(expressOauth());

    app.service('authentication').hooks({
        around: {},
        after: {
            create: [
                (context: HookContext) => {
                    const {login} = context.result;
                    const authenticationConfig = context.app.get('authentication');
                    if (!login.isVerified && _get(authenticationConfig, 'verification', true)) {
                        throw new NotAuthenticated('Login is not yet verified.');
                    }
                    const defaultWhitelist = ['_id', 'createdAt', 'updatedAt'];
                    let _login;
                    let enforceWhitelist = _get(authenticationConfig, 'enforceWhitelist', true);
                    let whitelistLoginFields = _get(authenticationConfig, 'whitelistLoginFields', ['']) || [''];
                    if (enforceWhitelist || whitelistLoginFields.length) _login = _pick(login, whitelistLoginFields.concat(defaultWhitelist));

                    let blacklistLoginFields = _get(authenticationConfig, 'blacklistLoginFields', []) || [];
                    _login = _unset(_login, blacklistLoginFields);
                    context.params = _set(context.params, 'configKey', configKey);
                    context.dispatch = Object.assign({}, context.result, {login: _login});
                    return context;
                }
            ]
        }
    });
};


