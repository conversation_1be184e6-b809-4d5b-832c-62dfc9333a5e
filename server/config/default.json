{"host": "localhost", "port": 3030, "public": "./public/", "root_url": "", "paginate": {"default": 10, "max": 1000}, "banking": {"moov": {}, "stripe": {"apiVersion": "2024-06-20"}, "ramp": {}}, "openai": {}, "x": {}, "google": {}, "cms": {}, "encryption": {}, "authentication": {"api_config": {"headers": {"id": "client_id", "key": "api_key"}}, "entity": "login", "service": "logins", "defaultScheme": "symbolDb", "defaultHierPart": "commoncare/*", "core_path": "core", "ucan_path": "ucan", "client_ucan": "core.client_ucan", "ucan_aud": "core.ucan_aud", "authStrategies": ["jwt", "local", "ucan", "o<PERSON>h", "webauthn"], "jwtOptions": {"header": {"typ": "access"}, "audience": "0.0.0.0", "issuer": "feathers", "algorithm": "none", "expiresIn": "1d"}, "local": {"usernameField": "email", "passwordField": "password"}, "webauthn": {"usernameField": "email", "passwordField": "password", "entity": "login", "service": "logins"}, "oauth": {"defaults": {}, "google": {"scope": ["openid", "profile", "email"], "nonce": true, "name": "google"}, "facebook": {"scope": ["email", "public_profile"], "name": "facebook"}, "linkedin": {"name": "linkedin", "authorize_url": "https://www.linkedin.com/oauth/v2/authorization", "access_url": "https://www.linkedin.com/oauth/v2/accessToken", "oauth": 2, "scope_delimiter": " ", "scope": ["openid", "profile", "email"], "custom_params": {"response_type": "code"}}, "github": {}}}, "mailer": {}, "sms": {}, "s3": {}, "video": {}, "uploads": {"privateFolder": "../private-files", "services": {"s3": false, "local-private": true, "local-public": true, "google-cloud": true, "grid-fs": true}, "defaultFileService": "local-public", "blockDeleteDocumentWhenDeleteFileFailed": false, "blockUpdateDocumentWhenReplaceFileFailed": false, "enums": {"s3": "s3", "local-private": "local-private", "local-public": "local-public", "google-cloud": "google-cloud", "digital-ocean": "digital-ocean", "grid-fs": "grid-fs", "others": "others", "UPLOAD_SERVICES": {"google-cloud": "google-cloud", "digital-ocean": "digital-ocean"}, "UPLOAD_PUBLIC_FILE_KEY": "public-file"}}, "salesTax": {}, "turnstile": {}}