<template>
  <div>
    <div class="__table">
      <table>
        <tbody>
        <template v-for="(r, i) in Object.keys(rows)" :key="`r-${i}`">
          <tr v-if="!rows[r].hide" class="__section">
            <td>{{ rows[r].label }}</td>
            <td></td>
          </tr>
          <tr v-show="!rows[r].hide" v-for="(row, idx) in rows[r].rows" :key="`row-${i}-${idx}`">
            <td>{{ row.label }}
              <q-tooltip v-if="row.tooltip" class="font-3-4r tw-five">{{ row.tooltip }}</q-tooltip>
            </td>
            <td>{{ row.value }}</td>
          </tr>
        </template>
        </tbody>
      </table>

      <div class="__prm">
        Premiums
      </div>
      <table>
        <tbody>
        <template v-if="household">
          <tr class="__p1">
            <td>Your Household</td>
            <td>{{ getPremium(coverage, 'single', { ages: household.people.map(a => a.age), display: true }) }}<span class="font-3-4r tw-five">/mo</span></td>
          </tr>
          <tr v-for="(prs, i) in household.people" :key="`prs-${i}`">
            <td>Age {{prs.age || getAge(prs.dob)}}</td>
            <td>{{getPremium(coverage, 'single', { ages: [prs.age || getAge(prs.dob)], display: true })}}</td>
          </tr>
        </template>
        <template v-else>
          <tr v-for="k in ['single', 'plus_spouse', 'plus_child','family']" :key="`p-${k}`">
            <td>{{ rateLabels[k] }}</td>
            <td>{{ getPremium(coverage, k, { display: true }) }}</td>
          </tr>
        </template>
        </tbody>
      </table>
    </div>
  </div>
</template>

<script setup>
  import {computed} from 'vue';
  import {dollarString} from 'src/utils/global-methods';
  import {getPremium} from 'src/components/coverages/utils/display';
  import {getAge} from 'src/components/enrollments/ichra/utils';

  const rateLabels = {
    single: 'Single',
    plus_spouse: 'Plus Spouse',
    plus_child: 'Plus Child',
    plus_child__2: '2 Children',
    plus_child__3: '3+ Children',
    family: 'Family'
  }

  const props = defineProps({
    modelValue: { required: true },
    household: Object,
    age: Number
  })

  const coverage = computed(() => props.modelValue || {});

  const rows = computed(() => {
    return {
      'deductible': {
        hide: !coverage.value.deductible,
        label: 'Deductible',
        rows: [
          {
            tooltip: coverage.value.deductible?.detail,
            label: `${coverage.value.deductible?.name || ''} Single/Family`,
            value: `${dollarString(coverage.value.deductible?.single, '$', 0)} / ${dollarString(coverage.value.deductible?.family, '$', 0)} per ${coverage.value.deductible?.type === 'event' ? 'event' : 'year'}`
          },
          ...Object.values(coverage.value.deductibles || {}).map(a => {
            return {
              tooltip: a.detail,
              label: `${a.name || ''} Single/Family`,
              value: `${dollarString(a.single, '$', 0)} / ${dollarString(a.family, '$', 0)} per ${a.type === 'event' ? 'event' : 'year'}`
            }
          })
        ]
      },
      'coinsurance': {
        hide: !coverage.value.coinsurance,
        label: 'Coinsurance',
        rows: [
          {
            tooltip: coverage.value.coinsurance?.detail,
            label: coverage.value.coinsurance?.name || 'Default',
            value: `${dollarString(coverage.value.coinsurance?.amount, '', 0)}%`
          },
          ...Object.values(coverage.value.coins || {}).map(a => {
            return {
              tooltip: a.detail,
              label: a.name,
              value: `${dollarString(a.amount, '', 0)}%`
            }
          })
        ]
      },
      'cap': {
        hide: !coverage.value.cap,
        label: 'Max Benefit',
        rows: [
          {
            tooltip: coverage.value.cap?.detail,
            label: `${coverage.value.cap?.name || ''} Single/Family`,
            value: `${dollarString(coverage.value.cap?.single, '$', 0)} / ${dollarString(coverage.value.cap?.family, '$', 0)} per ${coverage.value.cap?.type === 'event' ? 'event' : 'year'}`
          },
          ...Object.values(coverage.value.caps || {}).map(a => {
            return {
              tooltip: a.detail,
              label: `${a.name || ''} Single/Family`,
              value: `${dollarString(a.single, '$', 0)} / ${dollarString(a.family, '$', 0)} per ${a.type === 'event' ? 'event' : 'year'}`
            }
          })
        ]
      },
      'moop': {
        hide: typeof coverage.value.moop?.single !== 'number',
        label: 'Max Out of Pocket',
        rows: [
          {
            tooltip: coverage.value.moop?.detail,
            label: `${coverage.value.moop?.name || ''} Single/Family`,
            value: `${dollarString(coverage.value.moop?.single, '$', 0)} / ${dollarString(coverage.value.moop?.family, '$', 0)} per ${coverage.value.moop?.type === 'event' ? 'event' : 'year'}`
          },
          ...Object.values(coverage.value.moops || {}).map(a => {
            return {
              tooltip: a.detail,
              label: `${a.name || ''} Single/Family`,
              value: `${dollarString(a.single, '$', 0)} / ${dollarString(a.family, '$', 0)} per ${a.type === 'event' ? 'event' : 'year'}`
            }
          })
        ]
      }
    }
  })
</script>

<style lang="scss" scoped>

  .__table {
    width: 100%;
    overflow-x: scroll;

    table {
      width: 100%;
      border-collapse: collapse;

      tr {

        td {
          font-weight: 400;
          text-align: left;
          font-size: .9rem;
          border-bottom: solid .3px var(--ir-light);
          padding: 4px 8px;
          font-family: var(--alt-font);

          &:first-child {
            width: 50%;
            padding-left: 12px;
            font-size: .8rem;
            border-right: solid 1px var(--ir-light);
          }

          &:nth-child(2) {
            text-align: right;
            border-left: solid 1px var(--ir-light);
          }
        }
      }

      .__section {
        td {
          padding-left: 8px !important;
          font-weight: 500;
          background: var(--ir-bg1);
        }
      }

      .__p1 {
        td {
          font-weight: 600;
          color: var(--q-p5);
          background: var(--q-p0);
          border-bottom: none;
          font-size: 1rem;

          &:first-child {
            border-radius: 8px 0 0 0;
          }

          &:last-child {
            border-radius: 0 8px 0 0;
          }
        }
      }
    }
  }

  .__prm {
    font-size: 1rem;
    padding: 10px;
    font-weight: 500;
    color: var(--ir-mid);
  }
</style>
