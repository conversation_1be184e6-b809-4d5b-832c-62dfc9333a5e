<template>
  <div class="__coverage_viewer">
    <template v-if="mv.acaPlan">
      <policy-card
          :shop="shop"
          :enrollment="ermt"
          :mult="shop.mult"
          :aptc="shop.aptc"
          :popup="false"
          :dark="dark"
          :model-value="{...mv, premium}"
          :subsidy="subsidy"
          :enrolled="enrolled"
      ></policy-card>
    </template>
    <template v-else>
      <coverage-page
          :age="age"
          :enrolled="enrolled"
          :enrollment="ermt"
          :dark="dark"
          :model-value="{...mv, premium}"
          :mult="shop.mult"
          :subsidy="subsidy"
      ></coverage-page>
    </template>


    <div class="row justify-center" v-if="actualAptc && pt === 'aca'">
      <div class="__ptc">
        <div>You have <span class="text-p5">{{ dollarString(actualAptc, '$', 0) }}</span><span
            class="text-xxs tw-four">{{ shop.mult === 12 ? '/yr' : '/mo' }}</span> in subsidy to spend on this policy
        </div>
        <div class="text-xxs tw-six">These results are {{ shop.useAptc ? '' : 'NOT' }} taking this into account</div>
      </div>
    </div>

    <div class="row justify-center" v-if="shop.enrollment && employer">
      <div class="__er">
        <div>You have <span class="text-accent">{{ dollarString(employer * (shop.mult || 1), '$', 0) }}</span><span
            class="text-xxs tw-four">{{ shop.mult === 12 ? '/yr' : '/mo' }}</span> in allowance to spend on this option
        </div>
        <div class="text-xxs tw-six">This allowance is not taken into account above because you can opt to spend it
          elsewhere
        </div>
      </div>
    </div>

    <div class="row justify-end q-pa-md">
      <q-btn :disable="loading" size="lg" class="tw-six" color="primary" glossy @click="setPolicy" push>
        <span class="q-mr-sm">Choose This</span>
        <q-icon v-if="!loading" name="mdi-chevron-right"></q-icon>
        <q-spinner size="20px" v-else color="white"></q-spinner>
      </q-btn>
    </div>

    <div class="row justify-center q-pa-md">
      <div class="w500 mw100 relative-position cursor-pointer">
        <q-input input-class="tw-five" placeholder="Ask CommonAi about this plan" borderless
                 :class="`${tab === 'chat' ? '__hide' : '__chat_prompt'}`" @focus="tab = 'chat'" v-model="chatText">
          <template v-slot:prepend>
            <ai-logo opaque size="20px"></ai-logo>
          </template>
        </q-input>
      </div>
    </div>

    <div class="row q-pt-lg q-pb-sm">
      <q-tabs inline-label no-caps indicator-color="accent" align="left" v-model="tab" :dark="dark">
        <q-tab name="chat" v-if="tab === 'chat'">
          <ai-logo size="20px"></ai-logo>
          <span class="q-ml-sm">Chat</span>
        </q-tab>
        <q-tab name="rank" label="Rank"></q-tab>
        <q-tab name="outcomes" label="Outcomes"></q-tab>
        <q-tab v-if="pt === 'aca'" name="benefits" label="Benefits"></q-tab>
      </q-tabs>
    </div>

    <div class="__tps">

      <q-tab-panels v-model="tab" class="_panel" animated>
        <q-tab-panel keep-alive class="_panel" name="chat">
          <coverage-ai-chat
              :compare_coverages="compare_coverages"
              :current_coverage="current_coverage"
              :coverage="mv"
              :household="household"
          ></coverage-ai-chat>
        </q-tab-panel>
        <q-tab-panel class="_panel" name="rank">
          <div class="text-xs tw-six q-py-md text-ir-mid">How we ranked this option among all coverages</div>

          <div class="__rank" v-for="(rank, i) in ranks" :key="`rank-${i}`">
            <div>{{ rank.label }}</div>
            <div v-html="rank.rank"></div>
          </div>
        </q-tab-panel>
        <q-tab-panel class="_panel" name="outcomes">
          <div class="text-xs tw-six q-py-md text-ir-mid">How this product performs when you have medical bills
            totaling:
          </div>
          <div class="__rank tw-six">
            <div class="text-xxs text-ir-mid">Ann. Medical Bills</div>
            <div class="text-xxs text-ir-mid">Avg Premium + OOP (rank)</div>
          </div>
          <div class="__rank" v-for="(bench, i) in Object.keys(scores.dist)" :key="`dist-${i}`">
            <div class="alt-font text-xxs tw-six">{{ distLabels[bench] }}</div>
            <div :class="`alt-font tw-eight text-${rankColor(scores.ranks[bench])}`">
              {{ dollarString((scores.dist[bench] || {})[mv._id], '$', 0) }} <span
                class="text-xxs tw-six">({{ ordinalSuffixOf(scores.ranks[bench]) }} of {{ total_options }})</span></div>
          </div>
        </q-tab-panel>
        <q-tab-panel v-if="pt === 'aca'" class="_panel" name="benefits">
          <policy-detail :policy="mv"></policy-detail>
        </q-tab-panel>
      </q-tab-panels>
    </div>
  </div>
</template>

<script setup>
  import PolicyCard from 'components/enrollments/ichra/cards/PolicyCard.vue';
  import CoveragePage from 'components/coverages/pages/CoveragePage.vue';
  import PolicyDetail from 'components/enrollments/ichra/cards/PolicyDetail.vue';
  import AiLogo from 'src/utils/icons/AiLogo.vue';
  import CoverageAiChat from 'components/coverages/ai/chat/CoverageAiChat.vue';

  import {computed, ref} from 'vue';
  import {idGet} from 'src/utils/id-get';
  import {usePlans} from 'stores/plans';
  import {useEnrollments} from 'stores/enrollments';
  import {dollarString} from 'symbol-syntax-utils';
  import {distLabels, ordinalSuffixOf} from 'components/market/utils';
  import {darkness} from 'src/utils/env/darkness';
  import {erSubsidy} from 'components/market/shop/utils/subsidy';
  import {usePpls} from 'stores/ppls';
  import {getCoverageRate} from 'components/coverages/utils/display';
  import {useShops} from 'stores/shops';


  const planStore = usePlans()
  const erStore = useEnrollments();
  const pplStore = usePpls();
  const shopStore = useShops();

  const props = defineProps({
    compare_coverages: Object,
    current_coverage: String,
    dummyIds: Array,
    age: Number,
    enrolled: Array,
    modelValue: Object,
    raw: Object,
    byId: Object,
    enrollment: Object,
    open: Function,
    prompts: Array,
    shop: { type: Object, required: true }
  })
  const { dark } = darkness()

  const household = computed(() => {
    return {
      place: props.shop.stats?.place,
      people: [{ age: props.shop.stats?.age, gender: props.shop.stats?.gender, smoker: props.shop.stats?.smoker, child: false, relation: 'self' }, ...(props.shop.stats?.people || [])],
      income: props.shop.stats?.people
    }
  })

  const chatText = ref('');

  const mv = computed(() => props.modelValue || {})
  const pt = computed(() => mv.value.acaPlan ? 'aca' : mv.value.type)

  const actualAptc = computed(() => {
    if (!mv.value?.acaPlan || mv.value.off_exchange) return 0;
    return Math.min(mv.value.premium || 0, mv.value.aptc_eligible_premium || 0, props.shop.aptc) * (props.shop.mult || 1)
  })

  const premium = computed(() => {
    const { coverage_scores = {} } = props.shop || {}
    if(coverage_scores[mv.value._id]) return coverage_scores[mv.value._id].premium / 12;
    if(typeof mv.value.premium === 'number') return mv.value.premium / 12;
    return getCoverageRate({ coverage: mv.value, def_age: props.def_age, def_key: props.def_key })
  })

  const tab = ref('rank');
  const { item: plan } = idGet({
    store: planStore,
    value: computed(() => props.shop.plan)
  })

  const { item: ermt } = idGet({
    store: erStore,
    value: computed(() => props.shop.enrollment)
  })

  const { item: person } = idGet({
    store: pplStore,
    value: ermt.value.person
  })

  const { employer, subsidy } = erSubsidy({
    plan,
    enrollment: ermt,
    coverageId: computed(() => props.shop.plan_coverage),
    person
  })

  const loading = ref(false);
  const setPolicy = async () => {
    loading.value = true;
    const setKey = mv.value.acaPlan ? 'policy' : 'coverage';
    const unsetKey = { 'policy': 'coverage', 'coverage': 'policy' }
    // const idPath = mv.value.acaPlan ? 'plan_id' : '_id';
    const idPath = '_id';
    const id = mv.value[idPath]
    if (id && (!props.dummyIds || !props.dummyIds.includes(id))) await shopStore.patch(props.shop._id, {
      [setKey]: mv.value[idPath],
      $unset: { [unsetKey[setKey]]: '' }
    })
    if (props.open) props.open(mv.value)
  }

  const scores = computed(() => {
    const raw = props.shop || {}
    const add = raw.useAptc ? '_ptc' : ''
    const skey = `coverage_scores${add}`;
    const key = `distribution${add}`;
    const scores = raw[skey]
    const dist = raw[key] || {}
    const dist_ranks = {};
    const arr = Object.keys(dist);
    const id = mv.value._id;
    for (let i = 0; i < arr.length; i++) {
      dist_ranks[arr[i]] = Object.keys(dist[arr[i]] || {}).sort((a, b) => dist[arr[i]][a] - dist[arr[i]][b])
    }
    const ranks = {};
    for (let i = 0; i < arr.length; i++) {
      ranks[arr[i]] = dist_ranks[arr[i]]?.indexOf(id) + 1
    }

    return { scores, dist, ranks }
  });

  const total_options = computed(() => (props.byId || {}).average?.length)

  const rankColor = (v, inv) => {
    if (!v) return 'ir-text'
    let good = 'p';
    let bad = 's';
    if (inv) {
      good = 's'
      bad = 'p'
    }
    if (v === 1) return `${good}5`
    if (v < 4) return `${good}6`
    const len = total_options.value
    const unit = v / len;
    if (unit < .5) return 'a6'
    if (unit < .8) return `${bad}5`
    return `${bad}6`
  }

  const pctColor = (v, inv) => {
    let good = 'p';
    let bad = 's';
    if (inv) {
      good = 's'
      bad = 'p'
    }
    if (!v) return `${bad}6`
    if (v < 30) return `${bad}5`
    if (v < 50) return `a${inv ? 'ccent' : '6'}`
    if (v < 60) return `a${inv ? '6' : 'ccent'}`
    if (v < 85) return `${good}6`
    return `${good}5`

  }

  const ranks = computed(() => {
    const { average, blend_rank } = props.byId || {};
    const id = mv.value._id;
    const length = total_options.value
    const blend = (blend_rank || {})[id];
    const avg = Array.isArray(average) ? average.indexOf(id) + 1 : 0
    const { top1, top3, last } = (scores.value?.scores || {})[id] || {}
    return [
      {
        label: 'Overall',
        rank: `<span class="tw-six text-${rankColor(blend)}">${ordinalSuffixOf(blend)}</span> of ${length}`
      },
      {
        label: `Avg Premium + OOP (${dollarString((scores.value?.scores || {})[id]?.average, '$', 0)})`,
        rank: `<span class="tw-six text-${rankColor(avg)}">${ordinalSuffixOf(avg)}</span> of ${length}`
      },
      {
        label: 'Best Choice',
        rank: `<span class="tw-six text-${pctColor(top1)}">${dollarString(top1 || 0, '', 0)}%</span> of outcomes`
      },
      {
        label: 'Top 3 Choice',
        rank: `<span class="tw-six text-${pctColor(top3)}">${dollarString(top3 || 0, '', 0)}%</span> of outcomes`
      },
      {
        label: 'Worst Choice',
        rank: `<span class="tw-six text-${pctColor(last, 's', 'p')}">${dollarString(last || 0, '', 0)}%</span> of outcomes`
      }
    ]
  })


</script>

<style lang="scss" scoped>
  .__coverage_viewer {
    padding: 4vh max(2vw, 20px);
    color: var(--ir-text);
    background: var(--ir-bg);
    height: 100%;
    overflow-y: scroll;
  }

  .__ptc {
    margin-top: 30px;
    padding: 20px 20px;
    border-radius: 8px;
    font-size: var(--text-xs);
    font-weight: 600;
    width: 100%;
    max-width: 600px;
    background: linear-gradient(90deg, var(--ir-bg2), var(--ir-bg));
  }

  .__er {
    margin: 10px 0;
    padding: 20px 20px;
    border-radius: 8px;
    font-size: var(--text-xs);
    font-weight: 600;
    width: 100%;
    max-width: 600px;
    background: linear-gradient(90deg, var(--ir-a), var(--ir-bg));
  }

  .__rank {
    padding: 10px min(15px, 3vw);
    width: 100%;
    display: grid;
    grid-template-columns: 1fr auto;
    align-items: center;
    font-size: var(--text-xs);
    border-bottom: solid 1px var(--ir-mid);

    &:last-child {
      border-bottom: none;
    }
  }

  .__tps {
    padding-bottom: 50px;
  }

  .__chat_prompt {
    opacity: 1;
    transition: all .4s;
    width: 100%;
    background: var(--ir-bg2);
    border-radius: 40px;
    padding: 0 15px;
  }

  .__hide {
    opacity: 0;
    transform: translate(0, 500%);
  }
</style>
