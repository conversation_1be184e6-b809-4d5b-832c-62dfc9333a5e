<template>
  <div class="_fw">
    <template v-if="localLoading">
      <div class="q-pa-xl row justify-center">
        <ai-logo opaque></ai-logo>
      </div>
    </template>
    <template v-else-if="printMode">
      <table>
        <tbody>
        <template v-for="(ee, i) in ees" :key="`ee-${i}`">
          <template v-for="(col, idx) in cols[ee.uid]" :key="`col-${i}-${idx}`">
            <tr :class="`${idx === 0 ? '__new_ee' : '__label'}`">
              <td>{{ col.label }}</td>
              <td></td>
            </tr>
            <tr v-for="(row, index) in col.rows" :key="`row-${i}-${idx}-${index}`"
                v-show="!col.hide"
                :class="`${index === col.rows.length - 1 ? '__last' : ''}`">
              <td>{{ row.label }}</td>
              <td>{{ row.value }}</td>
            </tr>
          </template>
        </template>
        </tbody>
      </table>
    </template>
    <q-tab-panels animated class="_panel" :model-value="!viewing">
      <q-tab-panel class="_panel" :name="false">
        <div class="_fw">
          <q-chip color="ir-bg2" clickable @click="viewing = ''">
            <q-icon name="mdi-chevron-left" color="primary" class="q-mr-sm"></q-icon>
            <span>All Results</span>
          </q-chip>
        </div>
        <individual-picker :use-ptc="usePtc" :gps="gps" :model-value="viewing"></individual-picker>
      </q-tab-panel>
      <q-tab-panel class="_panel" :name="true">
        <q-input v-if="!noSearch" @update:modelValue="setSearch" :model-value="eeSearch" class="q-my-md w500 mw100">
          <template v-slot:prepend>
            <q-icon name="mdi-magnify"></q-icon>
          </template>
        </q-input>
        <table v-for="(ee, i) in ees" :key="`ee-${i}`" @click="viewing = ee">
          <tbody>
          <template v-for="(col, idx) in cols[ee.uid]" :key="`col-${i}-${idx}`">
            <tr :class="`${idx === 0 ? '__new_ee' : '__label'}`">
              <td>{{ col.label }}</td>
              <td></td>
            </tr>
            <tr v-for="(row, index) in col.rows" :key="`row-${i}-${idx}-${index}`"
                v-show="!col.hide"
                :class="`${index === col.rows.length - 1 ? '__last' : ''}`">
              <td>{{ row.label }}</td>
              <td>{{ row.value }}</td>
            </tr>
          </template>
          </tbody>
        </table>
        <pagination-row
            v-if="gps.employees?.length > pageh.pagination.limit && !noPagination"
            :max="pageh.pagination.pageCount"
            :page-record-count="pageh.pageRecordCount"
            :pagination="pageh.pagination"
            :h$="pageh"
            :limit="pageh.pagination.limit"
        ></pagination-row>
      </q-tab-panel>
    </q-tab-panels>

  </div>
</template>

<script setup>
  import PaginationRow from 'components/utils/pagination/PaginationRow.vue';
  import IndividualPicker from 'components/compare/group/results/IndividualPicker.vue';
  import AiLogo from 'src/utils/icons/AiLogo.vue';

  import {computed, ref, watch} from 'vue';
  import {dollarString} from 'src/utils/global-methods';
  import {useShops} from 'stores/shops';

  const shopStore = useShops();

  const emit = defineEmits(['update:loading']);
  const props = defineProps({
    loading: Boolean,
    printMode: Boolean,
    noPagination: Boolean,
    noSearch: Boolean,
    usePtc: Boolean,
    gps: {
      required: true, default: () => {
        return {}
      }
    }
  })

  const viewing = ref('')

  const limit = computed(() => props.noSearch ? props.gps.employees?.length : 10);

  const eeSearch = ref('');
  const skip = ref(0);
  const ees = computed(() => {
    const list = props.gps.employees || [];
    if(props.printMode) return list.map(a => {
      if(!a.name) a.name = `${a.firstName} ${a.lastName}`
      return a;
    });
    return list.filter(a => {
      const name = `${a.firstName} ${a.lastName} ${a.email || ''}`
      return name.toLowerCase().includes(eeSearch.value.toLowerCase())
    }).slice(skip.value, limit.value + skip.value).map(a => {
      if(!a.name) a.name = `${a.firstName} ${a.lastName}`
      return a;
    });
  })

  const shopByEe = ref({});

  const cols = ref({});
  const setCols = () => {
    const getCols = (ee) => {
      const shop = shopByEe.value[ee.uid] || {};
      const scores = shop.coverage_scores || {};
      const scores_ptc = shop.coverage_scores_ptc || {};
      const currentPlan = props.gps.coverages[ee.coverage] || {}
      const currentScore = scores[ee.coverage] || {};
      const coverageObj = {};
      for (const cov of shop.coverages || []) coverageObj[cov._id] = cov;
      let bestId = props.usePtc ? ee.bestPlanPtc : ee.bestPlan;
      if (shop.coverage) bestId = shop.coverage;
      else if (shop.policy) bestId = shop.policy;
      const bestPlan = coverageObj[bestId]
      const bestScore = props.usePtc ? { ...scores[bestId], ...scores_ptc[bestId] } : { ...scores[bestId] }
      const altPlan = coverageObj[ee.altPlan] || {};
      const altScore = scores[ee.altPlan] || {};
      return [
        {
          label: ee.name,
          rows: [
            {
              label: 'Household Size',
              value: (ee.married === 'Y' ? 1 : 0) + (ee.deps || 0) + 1
            },
            {
              label: 'Simulated Avg Medical Bills',
              value: dollarString(shop.spend, '$', 0)
            },
            {
              hide: !props.usePtc,
              label: 'Available PTC',
              value: dollarString(shop.aptc * 12, '$', 0)
            }
          ]
        },
        {
          label: 'Current Plan',
          rows: [
            {
              label: 'Name',
              value: !currentPlan ? 'none' : `${currentPlan.carrierName || ''} ${currentPlan.name || ''}`
            },
            {
              label: 'Premium',
              value: dollarString(currentScore.premium, '$', 0)
            },
            {
              label: 'Avg OOP',
              value: dollarString(currentScore.oop, '$', 0)
            }
          ]
        },
        {
          label: 'Employee Choice',
          rows: [
            {
              label: 'Name',
              value: !bestPlan ? 'None' : `${bestPlan.carrierName || ''} ${bestPlan.name || ''}`
            },
            {
              label: 'Premium',
              value: dollarString(bestScore.premium, '$', 0)
            },
            {
              label: 'Avg OOP',
              value: dollarString(bestScore.oop, '$', 0)
            }
          ]
        },
        {
          label: 'Best Alternative',
          rows: [
            {
              label: 'Name',
              value: !altPlan ? 'None' : `${altPlan.carrierName || ''} ${altPlan.name || ''}`
            },
            {
              label: 'Premium',
              value: dollarString(altScore.premium, '$', 0)
            },
            {
              label: 'Avg OOP',
              value: dollarString(altScore.oop, '$', 0)
            }
          ]
        }
      ]
    }

    const obj = {};
    for (const ee of ees.value) {
      obj[ee.uid] = getCols(ee);
    }
    cols.value = obj;
    return obj
  }

  const localLoading = ref(false);
  watch(() => props.loading, (nv, ov) => {
    if(nv !== ov) localLoading.value = nv;
  }, { immediate: true })

  const setShops = async () => {
    localLoading.value = true;
    emit('update:loading', true);
    try {
      const shopIds = [];
      const eeByShop = {};
      for (let i = 0; i < ees.value.length; i++) {
        const ee = ees.value[i];
        if (ee.sim) {
          eeByShop[ee.sim] = ee.uid;
          const ex = shopStore.getFromStore(ee.sim).value;
          if (!ex) shopIds.push(ee.sim);
          else shopByEe.value[ee.uid] = { coverage_scores: {}, coverage_scores_ptc: {}, ...ex }
        }
        // for (const k of ['bestPlan', 'bestPlanPtc', 'altPlan']) {
        //   if (ee[k]) planIds[ee[k]] = true;
        // }
      }
      const handleShops = async ($skip = 0, $limit = 10) => {
        if (!shopIds.length) return;
        const fetched = await shopStore.find({
          query: { _id: { $in: shopIds }, $limit, $skip },
        })
        for (const shop of fetched.data) {
          shopByEe.value[eeByShop[shop._id]] = shop;
        }
        if(ees.value.length > $skip + $limit) return await handleShops($skip + $limit, $limit);
        else return
      }

      await handleShops(0, Math.min(limit.value, 25));
      setCols();
    } catch (e) {
      console.error(`Error loading shops: ${e.message}`)
    } finally {
      localLoading.value = false;
      emit('update:loading', false);
    }
  }

  const searchTo = ref()
  const setSearch = (val) => {
    clearTimeout(searchTo.value);
    searchTo.value = setTimeout(() => {
      eeSearch.value = val;
      skip.value = 0;
      setShops();
    }, 500)
  }

  const pageh = computed(() => {
    const total = props.gps.employees?.length || 0;
    return {
      total,
      toPage: (v) => {
        skip.value = Math.floor((v - 1) * limit.value)
        setShops();
      },
      pageRecordCount: Math.min(total, skip.value + limit.value),
      pagination: {
        currentPage: Math.floor(skip.value / limit.value) + 1,
        pageCount: Math.ceil((props.gps.employees?.length || 0) / limit.value),
        limit: limit.value
      }
    }
  })

  watch(() => props.gps, (nv, ov) => {
    if (nv && nv._id !== ov?._id) {
      setTimeout(() => {
        setShops();
      }, 250)
    }
  }, { immediate: true })

</script>

<style lang="scss" scoped>
  table {
    width: 100%;
    border-collapse: collapse;

    tr {
      th {
        padding: 8px;
        text-align: left;
        font-size: .95rem;
        font-weight: 600;
        color: var(--ir-deep);
      }

      td {
        font-family: var(--alt-font);
        padding: 4px 8px;
        text-align: left;
        font-weight: 500;
        color: var(--ir-text);
        font-size: 1rem;

        &:first-child {
          padding-left: 17px;
          font-weight: 500;
        }

        &:nth-child(2) {
          width: 60%
        }
      }

      &:last-child {
        td {
          border-bottom: none;
        }
      }
    }

    .__last {
      td {
        padding-bottom: 15px;
      }
    }

    .__label {
      td {
        background: var(--q-p0);
        font-weight: 600 !important;
        color: var(--q-p5);
      }
    }

    .__new_ee {
      td {
        padding: 8px;
        background: var(--q-primary);
        font-weight: 600 !important;
        color: white;
        white-space: nowrap;

        &:first-child {
          padding-left: 12px;
          border-radius: 8px 0 0 8px;
        }

        &:last-child {
          border-radius: 0 8px 8px 0;
        }
      }
    }
  }
</style>
