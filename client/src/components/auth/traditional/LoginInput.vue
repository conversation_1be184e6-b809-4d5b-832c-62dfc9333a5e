<template>
  <div class="_fw">

    <template v-if="type === 'email'">
      <email-field
          @focus="setFocus"
          @blur="setBlur"
          v-model="form.email"
          input-class="font-3-4r"
          placeholder="Email"
          dense
          hide-bottom-space
          @update:valid="setValid"
      ></email-field>
    </template>
    <template v-if="type === 'phone'">
      <phone-input
          @update:valid="setValid"
          :emit-value="true"
          v-model="form.phone"
          @focus="setFocus"
          @blur="setBlur"
          :input-attrs="{ dense: true, inputClass: 'font-3-4r' }"
      ></phone-input>
    </template>
    <div class="_fw">
      <password-input
          inputClass="font-3-4r"
          placeholder="Enter Password"
          @blur="setBlur"
          @focus="setFocus"
          dense
          v-model="form.password"
          @update:valid="setReady"
      ></password-input>
      <div class="row q-py-sm">
        <q-chip @click="openReset" color="transparent" class="tw-five text-accent font-3-4r" clickable label="Forgot Password?"></q-chip>
      </div>

      <q-slide-transition>
        <div class="_fw q-my-md" v-if="verify">
          <div class="font-3-4r text-weight-bold">Your account is not yet verified</div>
          <q-input
              @focus="setFocus"
              @blur="setBlur"
              input-class="font-3-4r"
              placeholder="Enter Verification PIN"
              v-model="pin"
              @keyup.enter="verifyPin"
              filled
              color="green"
          >
            <template v-slot:append>
              <q-btn dense size="sm" round icon="mdi-check" color="green" v-if="pin" @click="verifyPin">
              </q-btn>
            </template>
          </q-input>
          <div class="row q-py-sm">
            <div class="text-p9 font-1r" v-if="!resendable">Don't see your PIN? You can resend after 30 seconds</div>
            <q-btn v-else size="sm" label="Resend PIN" flat color="p9" icon-right="mdi-refresh" @click="resend"></q-btn>
          </div>
          <q-slide-transition>
            <div v-if="resendMsg" class="q-py-sm text-red-10">{{ resendMsg }}</div>
          </q-slide-transition>
        </div>
        <q-btn v-else-if="ready" v-bind="signBtn" @click="saveBtn()"></q-btn>
      </q-slide-transition>
    </div>

  </div>
</template>

<script setup>
  import PasswordInput from 'src/components/common/input/PasswordInput.vue';
  import EmailField from 'src/components/common/input/EmailField.vue';
  import PhoneInput from 'src/components/common/phone/PhoneInput.vue';

  import {computed, ref} from 'vue';

  import {Notify} from 'quasar';
  import {isEmailRule} from 'src/stores/validate/validators';
  import {$errNotify, $successNotify} from 'src/utils/global-methods';

  import {LocalStorage, SessionStorage} from 'symbol-auth-client';
  import {useAuth} from 'src/stores/auth';
  import {useLogins} from 'src/stores/logins';

  const store = useLogins();

  const emit = defineEmits(['update:model-value', 'update:password', 'update:ready', 'focus', 'blur', 'save', 'reset']);
  const props = defineProps({
    type: { type: String },
    openFocusOn: Boolean,
    isReady: Boolean,
    password: String,
    modelValue: String
  });

  const focusOn = ref(false);

  let settingFocus = ref(false);
  const setFocus = () => {
    settingFocus.value = true;
    focusOn.value = true;
    emit('focus');
    setTimeout(() => {
      settingFocus.value = false;
    }, 500);
  };
  const setBlur = () => {
    setTimeout(() => {
      if (!settingFocus.value) {
        focusOn.value = false;
        emit('blur');
      }
    }, 250);
  };

  const login = ref(null);
  const v = ref(false);
  const ready = ref(false);
  const pin = ref('');
  const setReady = val => {
    ready.value = val;
    emit('update:ready', val);
  };

  const errNotify = (msg) => {
    Notify.create({
      message: msg,
      color: 'red',
      timeout: 2000,
      position: 'bottom'
    });
  };


  const formDef = (defs) => {
    return {
      email: '',
      phone: '',
      password: '',
      ...defs
    };
  };
  const form = ref(formDef());
  const matchTotal = ref(0);
  const existing = ref(undefined);
  const verify = ref(false);

  const runLogin = async (opts) => {
    LocalStorage.setItem('ucan_aud', opts.did);
    LocalStorage.setItem('client_ucan', opts.ucan);
    SessionStorage.setItem('client_ucan', opts.ucan);
    const auth = useAuth()
    return await auth.authenticate({ strategy: 'local', ...opts })
        .catch(err => $errNotify(err.message));
    // if(auth.isAuthenticated) router.go(0);
  };


  const verifyPin = async () => {
    const l = await store.patch(login.value?._id, {}, { query: { loginOptions: { verify: true, pin: pin.value } } })
        .catch(err => errNotify(err.message));
    if (l.isVerified) {
      const { email, phone, did, ucan } = l;
      await runLogin({ did, email, phone, password: form.value.password, ucan })
      window.open(SessionStorage.getItem('after-login-path') || window.location.origin, '_self');
    }

  };

  const resendable = ref(false);
  let timeout = undefined;
  const startTimer = () => {
    if (timeout) clearTimeout(timeout);
    else timeout = setTimeout(() => {
      resendable.value = true;
    }, 30000)
  }

  const resendMsg = ref('');
  const resend = async () => {
    if (existing.value?._id) {
      const v = await store.patch(existing.value._id, {}, { query: { loginOptions: { resend: 'email' } } });
      if (v) {
        login.value = v;
        verify.value = true;
        resendMsg.value = '';
        $successNotify('PIN resent');
        resendable.value = false;
        startTimer();
      }
    } else resendMsg.value = 'No login found for this email'
  }

  const openReset = () => {
    emit('reset')
  };

  const save = () => {
    if (existing.value?.isVerified) {
      const { email, phone, did, ucan } = existing.value;
      runLogin({ did, email, ucan, phone, password: form.value.password })
    } else if (existing.value?._id) {
      resend()
    } else if (form.value.email) {
      const valid = isEmailRule(form.value.email);
      if (!valid) errNotify('Invalid Email');
      else {
        const params = {
          ltail: window.location.href,
          query: { loginOptions: { method: 'email', resend: existing.value?._id ? 'email' : undefined } }
        };
        // console.log('I here now', params)
        store.new(form.value).save(params)
            .then(res => {
              login.value = res;
              startTimer();
              verify.value = true;
              if (!res.isVerified) $successNotify('Check your email to verify login!');
              else {
                const { did, email, phone, ucan } = res;
                runLogin({ did, email, phone, ucan, password: form.value.password })
              }
            })
            .catch(err => errNotify(err.message));
      }
    } else if (form.value.phone) {
      const valid = lastValid.value === form.value.phone;
      if (!valid) errNotify('Invalid Phone Number');
      else {
        store.new(form.value).save({ ltail: window.location.href, query: { loginOptions: { method: 'sms' } } })
            .then(res => {
              login.value = res;
              verify.value = true;
              if (!res.isVerified) $successNotify('Check your email to verify login!');
              else {
                const { did, email, phone } = res;
                runLogin({ did, email, phone, password: form.value.password })
              }
            })
            .catch(err => errNotify(err.message));
      }
    }
  };


  let lastValid = ref('');
  const setValid = async (val) => {
    v.value = val;
    if (val) {
      setTimeout(async () => {
        const check = form.value[props.type];
        if (check !== lastValid.value) {
          lastValid.value = check;
          const query = { $limit: 1, [props.type]: check };
          const { total, data } = await store.find({ query, loginOptions: { existCheck: true } });
          matchTotal.value = total;
          existing.value = data[0];
        }
      }, 50);
    }
  };


  const signBtn = computed(() => {
    return {
      color: 'green',
      label: existing.value ? 'Login' : 'Create Login',
      iconRight: 'mdi-arrow-right',
      push: true,
      glossy: true,
      class: 'q-my-sm'
    };
  });

  const saveBtn = () => {
    save();
    setFocus();
  };

</script>

<style scoped>

</style>
